我需要开发一个web端的客服聊天界面，请帮我创建一个完整的响应式原型设计方案。

**功能需求：**
- 客服聊天对话界面，支持用户与客服实时文字交流
- 消息发送和接收功能（包含发送按钮点击和回车键发送）
- 消息历史记录显示，支持滚动查看历史消息
- 用户输入框和发送按钮
- 消息时间戳显示
- 消息发送状态指示（发送中、已发送、已读等）
- 自动滚动到最新消息功能

**技术要求：**
- 使用HTML5 + CSS3 + JavaScript（原生JS，不依赖框架）实现
- 响应式设计，完美兼容PC端（桌面浏览器，最小宽度1200px）和移动端（手机320px-768px、平板768px-1024px）
- 采用CSS Flexbox布局技术
- 使用CSS媒体查询实现响应式断点
- 确保在不同屏幕尺寸下的良好用户体验
- 代码结构清晰，包含详细的中文注释

**设计要求：**
- 简洁现代的UI设计风格，采用扁平化设计
- 清晰的消息气泡样式区分用户消息（右侧，蓝色系）和客服消息（左侧，灰色系）
- 合适的颜色搭配：主色调使用蓝色系（#007bff），辅助色使用灰色系（#f8f9fa, #6c757d）
- 字体大小：PC端14-16px，移动端12-14px
- 移动端优化的触控交互体验，按钮最小触控区域44px
- 消息气泡圆角设计，阴影效果
- 头像显示功能（用户和客服）

**交付内容：**
- 完整的HTML文件（index.html）
- CSS样式文件（styles.css）
- JavaScript交互逻辑文件（script.js）
- 包含至少10条示例对话内容的演示效果
- 文件结构清晰，代码格式规范
- 每个文件都包含详细的中文注释说明

**文件保存位置：**
请将生成的文件保存在项目根目录下的 `doc/PrototypeDrawing/` 文件夹中，具体文件结构如下：
```
doc/PrototypeDrawing/
├── index.html
├── styles.css
└── script.js
```

**额外要求：**
- 确保代码使用4个空格缩进
- 优先保证代码简洁易懂
- 在实现完成后，建议编写简单的测试用例验证功能正常工作