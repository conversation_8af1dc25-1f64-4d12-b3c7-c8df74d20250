我需要开发一个web端的客服聊天界面，请帮我创建一个完整的响应式原型设计方案。

**功能需求：**
- 客服聊天对话界面，支持用户与客服实时文字交流
- 消息发送和接收功能（包含发送按钮点击和回车键发送）
- 消息历史记录显示，支持滚动查看历史消息
- 用户输入框和发送按钮
- 消息时间戳显示
- 消息发送状态指示（发送中、已发送、已读等）
- 自动滚动到最新消息功能
- 兼容PC浏览器和移动端的响应式设计

**技术要求：**
- 响应式设计，完美兼容PC端（桌面浏览器，最小宽度1200px）和移动端（手机320px-768px、平板768px-1024px）
- 确保在不同屏幕尺寸下的良好用户体验
- 使用 Tailwind CSS 4，通过CDN方式引入
- 如果需要使用图片，请使用网络图片链接（如 Unsplash、Picsum 等免费图片服务）
- 如果需要字体图标，请使用开源字体图标库，通过CDN引入（如 Font Awesome、Heroicons、Feather Icons 等）
- 创建完整的HTML文件，包含必要的CSS和JavaScript（如果需要）

**设计要求：**
- 简洁现代的UI设计风格，采用扁平化设计
- 清晰的消息气泡样式区分用户消息（右侧，蓝色系）和客服消息（左侧，灰色系）
- 合适的颜色搭配：主色调使用蓝色系（#007bff），辅助色使用灰色系（#f8f9fa, #6c757d）
- 字体大小：PC端14-16px，移动端12-14px
- 移动端优化的触控交互体验，按钮最小触控区域44px
- 消息气泡圆角设计，阴影效果
- 头像显示功能（用户和客服）
- 响应式设计，在不同屏幕尺寸下都有良好的用户体验
- 考虑用户体验，包括消息气泡样式、滚动行为、输入状态等细节

**交付内容：**
- 完整的HTML文件（index.html, 需包含css和js）
- 包含至少10条示例对话内容的演示效果
- 文件结构清晰，代码格式规范

**文件保存：**
- 将创建的原型文件保存到 `doc/PrototypeDrawing/` 文件夹中
- 文件命名要清晰，如 `customer-service-chat.html`
- 如果有多个文件，请使用有意义的文件名

请创建一个可以直接在浏览器中打开和使用的完整原型。