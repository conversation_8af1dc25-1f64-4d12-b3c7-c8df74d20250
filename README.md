# Customer Service Assistant | 客服助手

基于 Vue 3 + TypeScript + Tailwind CSS 构建的现代化客服助手应用。

## 技术栈

- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **类型检查**: TypeScript
- **样式框架**: Tailwind CSS v4
- **代码规范**: ESLint + Prettier

## 推荐的 IDE 设置

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (并禁用 Vetur)。

## TS 中对 `.vue` 导入的类型支持

默认情况下，TypeScript 无法处理 `.vue` 导入的类型信息，因此我们使用 `vue-tsc` 替代 `tsc` CLI 进行类型检查。在编辑器中，我们需要 [Volar](httpss://marketplace.visualstudio.com/items?itemName=Vue.volar) 来让 TypeScript 语言服务识别 `.vue` 类型。

## 自定义配置

请参阅 [Vite 配置参考](https://vite.dev/config/)。

## 项目设置

```sh
npm install
```

### 为开发环境编译和热重载

```sh
npm run dev
```

### 为生产环境进行类型检查、编译和压缩

```sh
npm run build
```

### 代码检查和格式化

#### ESLint 代码检查
使用 [ESLint](https://eslint.org/) 进行代码质量检查，配置了 Vue 3 + TypeScript 规则：

```sh
# 检查并自动修复代码问题
npm run lint
```

#### Prettier 代码格式化
使用 [Prettier](https://prettier.io/) 统一代码格式，保持代码风格一致：

```sh
# 格式化 src/ 目录下的所有文件
npm run format
```

### Tailwind CSS 样式开发

项目使用 Tailwind CSS v4 作为 CSS 框架：

- **实用优先**: 通过组合实用类快速构建界面
- **响应式设计**: 内置响应式断点，支持各种屏幕尺寸
- **深色模式**: 支持明暗主题切换
- **类型安全**: 与 TypeScript 完美集成

更多信息请参考 [Tailwind CSS 文档](https://tailwindcss.com/docs)。

## 开发流程

1. **安装依赖**: `npm install`
2. **启动开发服务器**: `npm run dev`
3. **代码检查**: `npm run lint`
4. **代码格式化**: `npm run format`
5. **类型检查**: `npm run type-check`
6. **生产构建**: `npm run build`